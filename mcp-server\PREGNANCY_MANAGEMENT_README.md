# Pregnancy Management Functionality

## Overview
This document describes the comprehensive pregnancy management functionality that has been implemented in the mcp-server. The system can detect user intent from natural language, extract relevant information, and format it into a specific JSON structure for saving to the Firestore database. It also includes AI-powered diet and health plan generation for pregnant animals.

## Features Implemented

### 1. Request Type Detection ✅
- Enhanced `analyzeRequestType` function to detect pregnancy-related requests
- Keywords detected: "pregnant", "pregnancy", "mating", "AI cross", "artificial insemination", "conception", etc.
- Returns `add_pregnancy` request type for proper routing

### 2. AI-Powered Data Extraction ✅
- Added `pregnancy` context to `analyzePromptWithAI` function
- Extracts: animalName, animalId, sireName, sireId, conceptionDate, status, isAICross
- Handles natural language input with high accuracy

### 3. Natural Language Date Parsing ✅
- Parses dates like "yesterday", "last Tuesday", "May 20, 2024", "3 days ago"
- Validates dates to ensure they're not in the future
- Returns ISO 8601 formatted dates

### 4. Data Validation ✅
- Validates all required fields before saving
- Resolves animal names to IDs using farm's animal list
- Handles AI cross logic (sets sireId to null, sireName to "AI Cross")
- Provides detailed error messages for missing information

### 5. AI-Powered Plan Generation ✅
- Generates weekly diet plans based on animal species and gestation period
- Generates weekly health plans with checkups and treatments
- Supports different gestation periods: Cow (280 days), Goat/Sheep (150 days), Pig (114 days)
- Plans are divided into early, mid, and late pregnancy stages

### 6. Database Operations ✅
- `savePregnancyToDatabase` function in database service
- Saves to `farms/{farmId}/pregnancies` collection
- Updates animal's pregnancy status
- Logs activity for tracking
- Handles errors gracefully

### 7. Multi-language Support ✅
- Supports both English and Urdu
- Localized error messages and responses
- Proper handling of missing information prompts

## API Usage

### Request Format
```javascript
POST /open-ai-chat
{
  "prompt": "Log a pregnancy for cow Daisy, sired by bull Titan on May 20, 2024",
  "userId": "user-id",
  "language": "en", // or "ur"
  "farms": [{ "id": "farm-id", "name": "Farm Name" }],
  "useAIPlans": true, // optional, defaults to false
  "context": {
    "availableAnimals": [
      { "id": "animal-id", "name": "Daisy", "species": "Cow" }
    ]
  }
}
```

### Response Format
```javascript
{
  "message": "✅ Pregnancy Record Saved Successfully!...",
  "pregnancySaved": true,
  "pregnancyId": "pregnancy-doc-id",
  "pregnancyData": {
    "farmId": "farm-id",
    "animalId": "animal-id",
    "animalName": "Daisy",
    "species": "Cow",
    "sireId": "sire-id",
    "sireName": "Titan",
    "conceptionDate": "2024-05-20T00:00:00.000Z",
    "status": "confirmed",
    "isAICross": false,
    "useAIPlans": true,
    "aiGeneratedDietPlan": [...],
    "aiGeneratedHealthPlan": [...],
    "createdAt": "2024-07-21T10:30:00.000Z",
    "createdBy": "user-id"
  }
}
```

## Supported Scenarios

### 1. Simple Mating
```
"Log a pregnancy for cow Daisy, sired by bull Titan on May 20, 2024. It's confirmed."
```

### 2. AI Cross with Plans
```
"Record an AI cross for goat Lily. It was done yesterday."
```

### 3. Missing Information
```
"Bessie is pregnant."
```
Response: Asks for missing conception date and sire information.

## Database Schema

### Pregnancy Document Structure
```javascript
{
  "farmId": "string",
  "animalId": "string", 
  "animalName": "string",
  "species": "string",
  "sireId": "string | null",
  "sireName": "string",
  "conceptionDate": "string (ISO 8601)",
  "status": "confirmed | suspected | not_pregnant",
  "isAICross": "boolean",
  "useAIPlans": "boolean",
  "aiGeneratedDietPlan": "Array<DietPlanItem> | null",
  "aiGeneratedHealthPlan": "Array<HealthPlanItem> | null",
  "createdAt": "string (ISO 8601)",
  "createdBy": "string"
}
```

### Diet Plan Item Structure
```javascript
{
  "week": "number",
  "stage": "early | mid | late",
  "title": "string",
  "description": "string", 
  "nutrients": ["string", "..."],
  "foods": ["string", "..."]
}
```

### Health Plan Item Structure
```javascript
{
  "week": "number",
  "stage": "early | mid | late", 
  "title": "string",
  "description": "string",
  "checkups": ["string", "..."],
  "treatments": ["string", "..."]
}
```

## Testing

### Utility Tests
Run `node test-pregnancy-utils.js` to test:
- Date parsing functions
- Data validation logic
- Missing information detection
- Multi-language message generation

### Integration Tests
Run `node test-pregnancy-management.js` to test:
- Complete pregnancy recording scenarios
- AI plan generation
- Error handling
- Multi-language support

## Files Modified/Created

### Modified Files
- `mcp-server/index.js` - Added pregnancy processing logic
- `mcp-server/services/ai-service.js` - Added pregnancy context and AI plan generators
- `mcp-server/services/database-service.js` - Added pregnancy database operations

### New Files
- `mcp-server/utils/pregnancyUtils.js` - Utility functions for pregnancy management
- `mcp-server/test-pregnancy-management.js` - Integration tests
- `mcp-server/test-pregnancy-utils.js` - Utility function tests
- `mcp-server/PREGNANCY_MANAGEMENT_README.md` - This documentation

## Error Handling
- Graceful handling of missing information
- Validation errors with user-friendly messages
- Fallback behavior when AI plan generation fails
- Database operation error handling

## Future Enhancements
- Pregnancy progress tracking
- Due date calculations and reminders
- Integration with health check system
- Breeding record management
- Pregnancy outcome tracking
