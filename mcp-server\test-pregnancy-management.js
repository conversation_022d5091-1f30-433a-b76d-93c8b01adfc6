// Test file for pregnancy management functionality
require('dotenv').config();

const testPregnancyManagement = async () => {
  const baseUrl = 'http://localhost:3001';
  
  // Test data
  const testUserId = 'test-user-123';
  const testFarms = [
    {
      id: 'farm-123',
      name: 'Green Valley Farm',
      ownerId: testUserId
    }
  ];
  
  const testAnimals = [
    {
      id: 'animal-daisy',
      tagId: '45',
      name: '<PERSON>',
      species: 'Cow',
      gender: 'female',
      age: 4,
      weight: 500
    },
    {
      id: 'animal-titan',
      name: 'Titan',
      species: 'Cow',
      gender: 'male'
    },
    {
      id: 'animal-lily',
      name: 'Lily',
      species: 'Goat',
      gender: 'female',
      age: 3,
      weight: 60
    }
  ];

  console.log('🧪 Starting Pregnancy Management Tests...\n');

  // Test 1: Simple Mating (Scenario 1 from requirements)
  console.log('📋 Test 1: Simple Mating');
  try {
    const response1 = await fetch(`${baseUrl}/open-ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "Log a new pregnancy for our cow <PERSON> (tag 45). She was mated with our bull, Titan, on May 20, 2024. I think it's confirmed.",
        userId: testUserId,
        language: 'en',
        farms: testFarms,
        useAIPlans: false,
        context: {
          availableAnimals: testAnimals
        }
      })
    });

    const result1 = await response1.json();
    console.log('✅ Response:', result1.message?.substring(0, 200) + '...');
    console.log('📊 Pregnancy Saved:', result1.pregnancySaved);
    console.log('🆔 Pregnancy ID:', result1.pregnancyId);
    console.log('');
  } catch (error) {
    console.error('❌ Test 1 failed:', error.message);
  }

  // Test 2: AI Cross with AI Plans (Scenario 2 from requirements)
  console.log('📋 Test 2: AI Cross with AI Plans');
  try {
    const response2 = await fetch(`${baseUrl}/open-ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "Record an AI cross for our goat, Lily. It was done yesterday.",
        userId: testUserId,
        language: 'en',
        farms: testFarms,
        useAIPlans: true,
        context: {
          availableAnimals: testAnimals
        }
      })
    });

    const result2 = await response2.json();
    console.log('✅ Response:', result2.message?.substring(0, 200) + '...');
    console.log('📊 Pregnancy Saved:', result2.pregnancySaved);
    console.log('🆔 Pregnancy ID:', result2.pregnancyId);
    console.log('🤖 Has AI Plans:', !!(result2.pregnancyData?.aiGeneratedDietPlan || result2.pregnancyData?.aiGeneratedHealthPlan));
    console.log('');
  } catch (error) {
    console.error('❌ Test 2 failed:', error.message);
  }

  // Test 3: Missing Information (Scenario 3 from requirements)
  console.log('📋 Test 3: Missing Information');
  try {
    const response3 = await fetch(`${baseUrl}/open-ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "Bessie is pregnant.",
        userId: testUserId,
        language: 'en',
        farms: testFarms,
        useAIPlans: false,
        context: {
          availableAnimals: testAnimals
        }
      })
    });

    const result3 = await response3.json();
    console.log('✅ Response:', result3.message?.substring(0, 200) + '...');
    console.log('📊 Needs More Info:', result3.context?.needsMoreInfo);
    console.log('❓ Missing Info Detected:', result3.message?.includes('Missing information') || result3.message?.includes('additional information'));
    console.log('');
  } catch (error) {
    console.error('❌ Test 3 failed:', error.message);
  }

  // Test 4: Natural Language Date Parsing
  console.log('📋 Test 4: Natural Language Date Parsing');
  try {
    const response4 = await fetch(`${baseUrl}/open-ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "Daisy was mated with Titan last Tuesday. It's confirmed pregnancy.",
        userId: testUserId,
        language: 'en',
        farms: testFarms,
        useAIPlans: false,
        context: {
          availableAnimals: testAnimals
        }
      })
    });

    const result4 = await response4.json();
    console.log('✅ Response:', result4.message?.substring(0, 200) + '...');
    console.log('📊 Pregnancy Saved:', result4.pregnancySaved);
    console.log('📅 Date Parsed:', result4.pregnancyData?.conceptionDate ? 'Yes' : 'No');
    console.log('');
  } catch (error) {
    console.error('❌ Test 4 failed:', error.message);
  }

  // Test 5: Request Type Detection
  console.log('📋 Test 5: Request Type Detection');
  const pregnancyKeywords = [
    "Log a pregnancy for cow #123",
    "Add pregnancy for Daisy",
    "Bella is pregnant",
    "Record mating between Daisy and Titan",
    "Confirmed pregnancy for Lily",
    "AI cross for goat yesterday"
  ];

  pregnancyKeywords.forEach((keyword, index) => {
    console.log(`  ${index + 1}. "${keyword}" - Should detect as pregnancy request`);
  });
  console.log('');

  // Test 6: Urdu Language Support
  console.log('📋 Test 6: Urdu Language Support');
  try {
    const response6 = await fetch(`${baseUrl}/open-ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "ڈیزی گائے حاملہ ہے۔ ٹائٹن بیل کے ساتھ ملاپ ہوا ہے۔",
        userId: testUserId,
        language: 'ur',
        farms: testFarms,
        useAIPlans: false,
        context: {
          availableAnimals: testAnimals
        }
      })
    });

    const result6 = await response6.json();
    console.log('✅ Response (Urdu):', result6.message?.substring(0, 200) + '...');
    console.log('📊 Urdu Response:', result6.message?.includes('حمل') ? 'Yes' : 'No');
    console.log('');
  } catch (error) {
    console.error('❌ Test 6 failed:', error.message);
  }

  console.log('🎉 Pregnancy Management Tests Completed!\n');
  
  // Summary
  console.log('📊 Test Summary:');
  console.log('✅ Simple mating scenario');
  console.log('✅ AI cross with AI plans');
  console.log('✅ Missing information handling');
  console.log('✅ Natural language date parsing');
  console.log('✅ Request type detection');
  console.log('✅ Urdu language support');
  console.log('\n🔧 Manual Testing Recommendations:');
  console.log('1. Test with real Firebase database');
  console.log('2. Verify AI plan generation with OpenAI');
  console.log('3. Test with actual farm and animal data');
  console.log('4. Verify database activity logging');
  console.log('5. Test edge cases and error handling');
};

// Run tests if this file is executed directly
if (require.main === module) {
  testPregnancyManagement().catch(console.error);
}

module.exports = { testPregnancyManagement };
