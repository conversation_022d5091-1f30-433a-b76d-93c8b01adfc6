const axios = require('axios');

// Debug script to test employee fetching for task assignment
async function debugEmployeeFetch() {
  console.log('🔍 Debugging Employee Fetch for Task Assignment...\n');

  // Test with a realistic farm setup
  const testData = {
    prompt: 'Create a task to fix the fence.',
    userId: 'test-user-123', // This should be a real user ID from your system
    language: 'en',
    farms: [{
      id: 'test-farm-456', // This should be a real farm ID
      name: 'Haven View', // Matching the farm name from your screenshot
      ownerId: 'test-user-123' // Should match the userId
    }]
  };

  try {
    console.log('📝 Sending task creation request...');
    console.log('Request data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post('http://localhost:3001/open-ai-chat', testData);
    
    console.log('\n✅ Response received');
    console.log('Status:', response.status);
    
    const result = response.data;
    
    if (result.error) {
      console.log('❌ Error response:', result.message);
    } else if (result.needsEmployeeSelection) {
      console.log('👥 Employee selection triggered');
      console.log('📋 Employee count:', result.employeeList?.length || 0);
      console.log('🎨 Selection type:', result.selectionType);
      
      if (result.employeeList && result.employeeList.length > 0) {
        console.log('\n👥 Available Employees:');
        result.employeeList.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.label || emp.name} (${emp.role})`);
          console.log(`     ID: ${emp.id}`);
          console.log(`     Image: ${emp.imageUri ? 'Available' : 'None'}`);
          console.log(`     Description: ${emp.description || 'N/A'}`);
        });
      } else {
        console.log('❌ No employees found in the response');
        console.log('📦 Full response:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('📄 Other response type:', result.message?.substring(0, 200));
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server not running on port 3001');
      console.log('💡 Please start the server with: node index.js');
    } else {
      console.log('❌ Request failed:', error.message);
      if (error.response) {
        console.log('📄 Response status:', error.response.status);
        console.log('📄 Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
}

// Also test with different farm/user combinations
async function testMultipleScenarios() {
  console.log('\n\n🧪 Testing Multiple Scenarios...\n');

  const scenarios = [
    {
      name: 'Real farm with owner as user',
      data: {
        prompt: 'Create a task to check the water pump.',
        userId: 'real-owner-id', // Replace with actual owner ID
        language: 'en',
        farms: [{
          id: 'real-farm-id', // Replace with actual farm ID
          name: 'Haven View',
          ownerId: 'real-owner-id'
        }]
      }
    },
    {
      name: 'Test scenario with mock data',
      data: {
        prompt: 'Create a task to feed the animals.',
        userId: 'mock-user-123',
        language: 'en',
        farms: [{
          id: 'mock-farm-456',
          name: 'Test Farm',
          ownerId: 'mock-user-123'
        }]
      }
    }
  ];

  for (const scenario of scenarios) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    try {
      const response = await axios.post('http://localhost:3001/open-ai-chat', scenario.data);
      
      if (response.data.needsEmployeeSelection) {
        console.log(`✅ Employee selection triggered - ${response.data.employeeList?.length || 0} employees found`);
      } else if (response.data.error) {
        console.log(`❌ Error: ${response.data.message}`);
      } else {
        console.log(`📄 Other response: ${response.data.message?.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`);
    }
  }
}

// Run the debug
async function runDebug() {
  await debugEmployeeFetch();
  // Uncomment to test multiple scenarios
  // await testMultipleScenarios();
  console.log('\n🏁 Debug completed!');
}

runDebug().catch(console.error);
