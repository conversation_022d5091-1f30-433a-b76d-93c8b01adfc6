// Utility functions for pregnancy management

// Parse natural language dates to ISO format
const parseNaturalDate = (dateString) => {
  if (!dateString) return null;
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  // Clean the input
  const cleanDate = dateString.toLowerCase().trim();
  
  // Handle relative dates
  if (cleanDate.includes('today')) {
    return today.toISOString();
  }
  
  if (cleanDate.includes('yesterday')) {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString();
  }
  
  if (cleanDate.includes('last tuesday') || cleanDate.includes('tuesday last week')) {
    const lastTuesday = new Date(today);
    const daysBack = (today.getDay() + 5) % 7 + 1; // Days back to last Tuesday
    lastTuesday.setDate(lastTuesday.getDate() - daysBack);
    return lastTuesday.toISOString();
  }
  
  // Handle "X days ago"
  const daysAgoMatch = cleanDate.match(/(\d+)\s*days?\s*ago/);
  if (daysAgoMatch) {
    const daysAgo = parseInt(daysAgoMatch[1]);
    const date = new Date(today);
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString();
  }
  
  // Handle "last week"
  if (cleanDate.includes('last week')) {
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    return lastWeek.toISOString();
  }
  
  // Handle "last month"
  if (cleanDate.includes('last month')) {
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    return lastMonth.toISOString();
  }
  
  // Try to parse standard date formats
  try {
    // Handle formats like "June 1st", "June 1st, 2024", "May 20, 2024"
    let parsedDate = new Date(dateString);
    
    // If no year specified, assume current year
    if (parsedDate.getFullYear() === 1970 || isNaN(parsedDate.getTime())) {
      // Try with current year
      const withYear = `${dateString}, ${now.getFullYear()}`;
      parsedDate = new Date(withYear);
    }
    
    // Validate the parsed date
    if (!isNaN(parsedDate.getTime()) && parsedDate.getTime() > 0) {
      // Ensure date is not in the future (more than 1 day ahead)
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      if (parsedDate <= tomorrow) {
        return parsedDate.toISOString();
      }
    }
  } catch (error) {
    console.warn('Error parsing date:', dateString, error);
  }
  
  return null;
};

// Validate pregnancy data before saving
const validatePregnancyData = (pregnancyData, animalList = []) => {
  const errors = [];
  const warnings = [];
  
  // Required fields validation
  if (!pregnancyData.farmId) {
    errors.push('Farm ID is required');
  }
  
  if (!pregnancyData.animalId && !pregnancyData.animalName) {
    errors.push('Animal ID or name is required');
  }
  
  // Find animal in the list if animalName is provided but animalId is missing
  if (pregnancyData.animalName && !pregnancyData.animalId && animalList.length > 0) {
    const animal = animalList.find(a => 
      a.name?.toLowerCase() === pregnancyData.animalName.toLowerCase() ||
      a.tagId === pregnancyData.animalName
    );
    
    if (animal) {
      pregnancyData.animalId = animal.id;
      pregnancyData.species = animal.species;
    } else {
      errors.push(`Animal "${pregnancyData.animalName}" not found in farm`);
    }
  }
  
  // Find sire in the list if sireName is provided but sireId is missing
  if (pregnancyData.sireName && !pregnancyData.sireId && !pregnancyData.isAICross && animalList.length > 0) {
    const sire = animalList.find(a => 
      a.name?.toLowerCase() === pregnancyData.sireName.toLowerCase() ||
      a.tagId === pregnancyData.sireName
    );
    
    if (sire) {
      pregnancyData.sireId = sire.id;
    } else {
      warnings.push(`Sire "${pregnancyData.sireName}" not found in farm - will save as external sire`);
    }
  }
  
  // Validate conception date
  if (pregnancyData.conceptionDate) {
    const parsedDate = parseNaturalDate(pregnancyData.conceptionDate);
    if (parsedDate) {
      pregnancyData.conceptionDate = parsedDate;
    } else {
      errors.push(`Invalid conception date: "${pregnancyData.conceptionDate}"`);
    }
  }
  
  // Validate status
  const validStatuses = ['confirmed', 'suspected', 'not_pregnant'];
  if (pregnancyData.status && !validStatuses.includes(pregnancyData.status)) {
    pregnancyData.status = 'suspected'; // Default to suspected
    warnings.push('Invalid status provided, defaulting to "suspected"');
  }
  
  // Set default status if not provided
  if (!pregnancyData.status) {
    pregnancyData.status = 'suspected';
  }
  
  // Validate AI cross logic
  if (pregnancyData.isAICross) {
    pregnancyData.sireId = null;
    pregnancyData.sireName = 'AI Cross';
  }
  
  // Ensure boolean values are properly set
  pregnancyData.isAICross = Boolean(pregnancyData.isAICross);
  pregnancyData.useAIPlans = Boolean(pregnancyData.useAIPlans);
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    validatedData: pregnancyData
  };
};

// Find missing required information for pregnancy
const findMissingPregnancyInfo = (pregnancyData) => {
  const missing = [];
  
  if (!pregnancyData.animalId && !pregnancyData.animalName) {
    missing.push('animal identification (name or ID)');
  }
  
  if (!pregnancyData.conceptionDate) {
    missing.push('conception date');
  }
  
  if (!pregnancyData.isAICross && !pregnancyData.sireId && !pregnancyData.sireName) {
    missing.push('sire information (was this natural mating or AI cross?)');
  }
  
  return missing;
};

// Generate user-friendly missing info message
const generateMissingInfoMessage = (missingInfo, language = 'en') => {
  if (missingInfo.length === 0) return null;
  
  const missingList = missingInfo.join(', ');
  
  if (language === 'ur') {
    return `حمل کی معلومات محفوظ کرنے کے لیے مجھے مزید تفصیلات درکار ہیں:\n\n❓ **مطلوبہ معلومات**: ${missingList}\n\nبراہ کرم یہ معلومات فراہم کریں۔`;
  }
  
  return `To record the pregnancy, I need some additional information:\n\n❓ **Missing information**: ${missingList}\n\nPlease provide these details.`;
};

module.exports = {
  parseNaturalDate,
  validatePregnancyData,
  findMissingPregnancyInfo,
  generateMissingInfoMessage
};
