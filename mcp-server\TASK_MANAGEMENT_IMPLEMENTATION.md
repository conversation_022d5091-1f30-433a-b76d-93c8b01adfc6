# Task Management Implementation

## Overview
This document describes the implementation of natural language task creation and assignment functionality in the mcp-server. The system can understand user requests to create tasks, extract task details, and handle employee assignment with a validation flow.

## Features Implemented

### 1. Request Type Detection
The system can detect task creation requests from natural language prompts:

**Keywords and Patterns:**
- "Create a new task"
- "Create a task for [employee name]"
- "Add a task"
- "Assign a task to [employee name]"
- "New task: [task title]"
- "Task: [task title]"
- "Remind [employee name] to..."

**Examples:**
- ✅ "Create a task for <PERSON> to clean the milking parlor tomorrow."
- ✅ "New task: Fix the fence. It's high priority."
- ✅ "Assign a task to <PERSON> to vaccinate the cattle."
- ✅ "Remind Mike to check the water pump."

### 2. AI-Powered Data Extraction
The system uses OpenAI to extract structured task information:

**Extracted Fields:**
- `title`: Main task description (required)
- `description`: Detailed task description
- `assigneeName`: Employee name (optional in initial prompt)
- `dueDate`: Natural language date parsing
- `priority`: high/medium/low
- `recurrence`: daily/weekly/monthly/none
- `notes`: Additional notes

**AI Context:** `add_task`

### 3. Employee Selection Validation Flow

#### Scenario 1: Assignee Mentioned
When an employee name is provided in the prompt:
1. System searches for the employee in the farm's assigned users
2. If found: Creates task immediately
3. If not found: Returns error with available employee list

#### Scenario 2: No Assignee Mentioned
When no employee is specified:
1. System fetches all employees assigned to the selected farm
2. Returns employee selection prompt with:
   - Task details
   - List of available employees (id, name, imageUri)
   - Context data for follow-up processing

#### Scenario 3: Employee Selection Follow-up
When user selects an employee:
1. System receives employee ID in prompt
2. Validates employee exists and is assigned to farm
3. Creates and saves complete task
4. Returns success confirmation

### 4. Database Integration

**Task Storage:**
- Path: `farms/{farmId}/tasks/{taskId}`
- Follows existing subcollection pattern
- Matches frontend Task interface structure

**Required Fields:**
- `title`: Task title
- `farmId`: Farm identifier
- `assignedTo`: Employee user ID
- `assigneeName`: Employee display name
- `assignedBy`: Creator user ID
- `status`: 'pending' (default)
- `priority`: 'high'|'medium'|'low'
- `dueDate`: Timestamp (optional)
- `recurrence`: 'daily'|'weekly'|'monthly'|'none'

**Activity Logging:**
- Automatically logs task creation to farm activities
- Includes task title, assignee, and due date

### 5. Date Parsing
Simple natural language date parsing:
- "tomorrow" → +1 day
- "next week" → +7 days  
- "friday" → Next Friday
- Standard date formats

## API Response Formats

### Employee Selection Required
```json
{
  "message": "Who should this task be assigned to?",
  "needsEmployeeSelection": true,
  "employeeList": [
    {
      "id": "emp-1",
      "name": "John Doe", 
      "imageUri": "url_to_image",
      "role": "caretaker"
    }
  ],
  "context": {
    "taskData": {
      "title": "Fix the fence",
      "farmId": "farm-123",
      "priority": "high"
    }
  }
}
```

### Task Created Successfully
```json
{
  "message": "✅ Task Created Successfully...",
  "taskData": { /* complete task object */ },
  "saved": true,
  "databaseId": "task-456"
}
```

### Error Response
```json
{
  "message": "Employee 'John' not found. Please choose from available employees.",
  "error": true
}
```

## Implementation Files

### 1. AI Service (`services/ai-service.js`)
- Added `add_task` context for task information extraction
- Comprehensive prompt engineering for task detection
- Returns structured JSON with task fields

### 2. Database Service (`services/database-service.js`)
- Added `saveTaskToDatabase()` function
- Follows existing patterns for subcollection storage
- Includes activity logging and validation

### 3. Main Server (`index.js`)
- Added task detection to `analyzeRequestType()`
- Implemented complete task creation flow
- Added employee selection handling
- Added `createAndSaveTask()` helper function

## Testing

### Test File: `test-task-management.js`
Comprehensive test suite covering:
- Task creation with assignee name
- Task creation without assignee (employee selection)
- Task creation with reminders
- Task creation with recurrence
- Employee selection flow

### Running Tests
```bash
node test-task-management.js
```

## Error Handling

**Common Error Scenarios:**
1. No farms available → Prompt to create farm first
2. No title provided → Request task description
3. No employees found → Prompt to add employees
4. Employee not found → Show available employees
5. Database errors → Generic retry message

## Multi-language Support
- All user-facing messages support Urdu (`ur`) and English (`en`)
- Language parameter passed through request body
- Consistent with existing codebase patterns

## Integration with Frontend
The implementation is designed to work seamlessly with the existing React Native frontend:
- Matches Task interface from `types/task.ts`
- Compatible with `services/task-service.ts`
- Follows same database structure as `app/tasks/add.tsx`

## Future Enhancements
1. More sophisticated date parsing (using libraries like chrono-node)
2. Task templates and categories
3. Bulk task creation
4. Task dependencies
5. Notification scheduling
6. Task completion tracking via natural language
