const axios = require('axios');

// Test configuration
const SERVER_URL = 'http://localhost:3001';
const TEST_USER_ID = 'test-user-123';
const TEST_FARM_ID = 'test-farm-456';

// Test data
const testFarm = {
  id: TEST_FARM_ID,
  name: 'Test Farm',
  ownerId: TEST_USER_ID
};

const testEmployee = {
  id: 'emp-123',
  name: '<PERSON>',
  role: 'caretaker'
};

// Test cases
const testCases = [
  {
    name: 'Task creation with assignee name',
    prompt: 'Create a task for <PERSON> to clean the milking parlor tomorrow.',
    expectedType: 'add_task',
    shouldNeedEmployeeSelection: false
  },
  {
    name: 'Task creation without assignee',
    prompt: 'New task: Fix the fence. It\'s high priority.',
    expectedType: 'add_task',
    shouldNeedEmployeeSelection: true
  },
  {
    name: 'Task creation with reminder',
    prompt: 'Remind <PERSON> to vaccinate the cattle next week.',
    expectedType: 'add_task',
    shouldNeedEmployeeSelection: false
  },
  {
    name: 'Task creation with recurrence',
    prompt: 'Create a daily task to feed the animals.',
    expectedType: 'add_task',
    shouldNeedEmployeeSelection: true
  }
];

async function testTaskManagement() {
  console.log('🧪 Starting Task Management Tests...\n');

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`📝 Prompt: "${testCase.prompt}"`);

    try {
      const response = await axios.post(`${SERVER_URL}/open-ai-chat`, {
        prompt: testCase.prompt,
        userId: TEST_USER_ID,
        language: 'en',
        farms: [testFarm]
      });

      const result = response.data;
      console.log('✅ Response received');
      
      if (result.error) {
        console.log('❌ Error:', result.message);
      } else if (result.needsEmployeeSelection) {
        console.log('👥 Employee selection required');
        console.log('📋 Available employees:', result.employeeList?.length || 0);
        console.log('📦 Task data stored in context:', !!result.context?.taskData);
        
        if (testCase.shouldNeedEmployeeSelection) {
          console.log('✅ Expected employee selection - PASS');
        } else {
          console.log('❌ Unexpected employee selection - FAIL');
        }
      } else if (result.saved) {
        console.log('💾 Task saved successfully');
        console.log('🆔 Database ID:', result.databaseId);
        
        if (!testCase.shouldNeedEmployeeSelection) {
          console.log('✅ Direct task creation - PASS');
        } else {
          console.log('❌ Expected employee selection but task was saved directly - FAIL');
        }
      } else {
        console.log('📄 Message:', result.message.substring(0, 100) + '...');
      }

    } catch (error) {
      console.log('❌ Request failed:', error.message);
    }
  }

  // Test employee selection flow
  console.log('\n\n👥 Testing Employee Selection Flow...');
  
  try {
    // First, create a task that needs employee selection
    const taskResponse = await axios.post(`${SERVER_URL}/open-ai-chat`, {
      prompt: 'Create a task to check the water pump.',
      userId: TEST_USER_ID,
      language: 'en',
      farms: [testFarm]
    });

    if (taskResponse.data.needsEmployeeSelection) {
      console.log('✅ Task creation triggered employee selection');
      
      // Now simulate employee selection
      const selectionResponse = await axios.post(`${SERVER_URL}/open-ai-chat`, {
        prompt: testEmployee.id, // Send employee ID as selection
        userId: TEST_USER_ID,
        language: 'en',
        context: {
          taskData: taskResponse.data.context.taskData,
          needsEmployeeSelection: true
        }
      });

      if (selectionResponse.data.saved) {
        console.log('✅ Employee selection and task creation successful');
        console.log('🆔 Final task ID:', selectionResponse.data.databaseId);
      } else {
        console.log('❌ Employee selection failed:', selectionResponse.data.message);
      }
    } else {
      console.log('❌ Expected employee selection but got:', taskResponse.data);
    }

  } catch (error) {
    console.log('❌ Employee selection test failed:', error.message);
  }

  console.log('\n🏁 Task Management Tests Complete!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testTaskManagement().catch(console.error);
}

module.exports = { testTaskManagement };
