const axios = require('axios');

// Test the exact scenario from your screenshot with real data
async function testFrontendIntegration() {
  console.log('🎯 Testing Frontend Integration with Employee Selection...\n');

  // Use the exact data from your database for Haven View farm
  const testRequest = {
    prompt: 'Create a task to fix the fence.',
    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', // Dr. Javad Ahmed
    language: 'en',
    farms: [{
      id: '7owH6eTfGSYqaRpgIsQE', // Haven View.
      name: 'Haven View.',
      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'
    }]
  };

  try {
    console.log('📤 Sending request to trigger employee selection...');
    console.log('Request:', JSON.stringify(testRequest, null, 2));

    const response = await axios.post('http://localhost:3001/open-ai-chat', testRequest);
    
    console.log('\n✅ Response received from server:');
    console.log('Status:', response.status);
    
    const data = response.data;
    
    // Check if this is the employee selection response
    if (data.needsEmployeeSelection) {
      console.log('\n🎉 SUCCESS! Employee selection response detected');
      console.log('📋 Response structure:');
      console.log('  - needsEmployeeSelection:', data.needsEmployeeSelection);
      console.log('  - selectionType:', data.selectionType);
      console.log('  - employeeList length:', data.employeeList?.length || 0);
      console.log('  - context present:', !!data.context);
      console.log('  - taskData present:', !!data.context?.taskData);

      if (data.employeeList && data.employeeList.length > 0) {
        console.log('\n👥 Employee List (Frontend Format):');
        data.employeeList.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.label} (${emp.role})`);
          console.log(`     ID: ${emp.id}`);
          console.log(`     Description: ${emp.description}`);
          console.log(`     Image: ${emp.imageUri ? 'Available' : 'None'}`);
        });

        console.log('\n📱 Frontend should now:');
        console.log('  1. Detect needsEmployeeSelection: true');
        console.log('  2. Show employee selection modal/dropdown');
        console.log('  3. Display the employee list with photos and names');
        console.log('  4. Allow user to select an employee');
        console.log('  5. Send selectedEmployeeId back to server');

        // Test selecting the first employee
        console.log('\n🧪 Testing employee selection...');
        const firstEmployee = data.employeeList[0];
        console.log(`Selecting: ${firstEmployee.label} (${firstEmployee.id})`);

        const selectionRequest = {
          prompt: '', // Empty since we're using selectedEmployeeId
          selectedEmployeeId: firstEmployee.id,
          userId: testRequest.userId,
          language: 'en',
          context: data.context,
          farms: testRequest.farms
        };

        console.log('\n📤 Sending employee selection...');
        const selectionResponse = await axios.post('http://localhost:3001/open-ai-chat', selectionRequest);

        if (selectionResponse.data.saved) {
          console.log('✅ Task created successfully!');
          console.log('🆔 Task ID:', selectionResponse.data.databaseId);
          console.log('📄 Success message:', selectionResponse.data.message.substring(0, 100) + '...');
          
          console.log('\n🎯 COMPLETE FLOW TESTED SUCCESSFULLY!');
          console.log('The frontend should:');
          console.log('  1. ✅ Show employee selection modal');
          console.log('  2. ✅ Allow user to select employee');
          console.log('  3. ✅ Send selection to server');
          console.log('  4. ✅ Display success message');
          console.log('  5. ✅ Close modal and return to chat');
        } else {
          console.log('❌ Task creation failed:', selectionResponse.data.message);
        }

      } else {
        console.log('❌ No employees in response');
      }

    } else {
      console.log('❌ Expected employee selection but got different response:');
      console.log('Message:', data.message);
      console.log('Error:', data.error);
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server not running on port 3001');
      console.log('💡 Please start the server with: node index.js');
    } else {
      console.log('❌ Request failed:', error.message);
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
  }
}

// Test with different scenarios
async function testMultipleScenarios() {
  console.log('\n\n🧪 Testing Multiple Task Creation Scenarios...\n');

  const scenarios = [
    {
      name: 'Task without assignee (should trigger employee selection)',
      prompt: 'Create a task to check the water pump.',
      shouldTriggerSelection: true
    },
    {
      name: 'Task with assignee name (should find employee directly)',
      prompt: 'Create a task for Ali to clean the milking parlor.',
      shouldTriggerSelection: false
    },
    {
      name: 'Task with reminder format',
      prompt: 'Remind Hassan to vaccinate the cattle tomorrow.',
      shouldTriggerSelection: false
    }
  ];

  for (const scenario of scenarios) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    console.log(`📝 Prompt: "${scenario.prompt}"`);

    try {
      const response = await axios.post('http://localhost:3001/open-ai-chat', {
        prompt: scenario.prompt,
        userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',
        language: 'en',
        farms: [{
          id: '7owH6eTfGSYqaRpgIsQE',
          name: 'Haven View.',
          ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'
        }]
      });

      if (response.data.needsEmployeeSelection) {
        console.log(`✅ Employee selection triggered (${response.data.employeeList?.length || 0} employees)`);
        if (scenario.shouldTriggerSelection) {
          console.log('✅ Expected behavior - PASS');
        } else {
          console.log('❌ Unexpected employee selection - REVIEW');
        }
      } else if (response.data.saved) {
        console.log('✅ Task created directly');
        if (!scenario.shouldTriggerSelection) {
          console.log('✅ Expected behavior - PASS');
        } else {
          console.log('❌ Expected employee selection - REVIEW');
        }
      } else {
        console.log('📄 Other response:', response.data.message?.substring(0, 50) + '...');
      }

    } catch (error) {
      console.log('❌ Failed:', error.message);
    }
  }
}

// Run the tests
async function runTests() {
  await testFrontendIntegration();
  await testMultipleScenarios();
  console.log('\n🏁 Frontend integration tests completed!');
  console.log('\n💡 Next steps:');
  console.log('1. Test the updated chat.tsx in your React Native app');
  console.log('2. Verify the employee selection modal appears');
  console.log('3. Test selecting an employee and creating the task');
  console.log('4. Check that the success message appears and modal closes');
}

runTests().catch(console.error);
