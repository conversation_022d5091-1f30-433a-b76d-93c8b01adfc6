const axios = require('axios');

// Simple test to verify task management functionality
async function testTaskCreation() {
  console.log('🧪 Testing Task Management Functionality...\n');

  const testData = {
    prompt: 'Create a task for <PERSON> to clean the milking parlor tomorrow.',
    userId: 'test-user-123',
    language: 'en',
    farms: [{
      id: 'test-farm-456',
      name: 'Test Farm'
    }]
  };

  try {
    console.log('📝 Sending request:', testData.prompt);
    
    const response = await axios.post('http://localhost:3001/open-ai-chat', testData);
    
    console.log('✅ Response received');
    console.log('📄 Status:', response.status);
    
    if (response.data.error) {
      console.log('❌ Error:', response.data.message);
    } else if (response.data.needsEmployeeSelection) {
      console.log('👥 Employee selection required');
      console.log('📋 Available employees:', response.data.employeeList?.length || 0);
      console.log('📦 Task data stored:', !!response.data.context?.taskData);
    } else if (response.data.saved) {
      console.log('💾 Task saved successfully!');
      console.log('🆔 Database ID:', response.data.databaseId);
    } else {
      console.log('📄 Response message:', response.data.message?.substring(0, 200) + '...');
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server not running on port 3001');
      console.log('💡 Please start the server with: node index.js');
    } else {
      console.log('❌ Request failed:', error.message);
      if (error.response) {
        console.log('📄 Response status:', error.response.status);
        console.log('📄 Response data:', error.response.data);
      }
    }
  }
}

// Test without assignee to trigger employee selection
async function testEmployeeSelection() {
  console.log('\n\n👥 Testing Employee Selection Flow...\n');

  const testData = {
    prompt: 'New task: Fix the fence. It\'s high priority.',
    userId: 'test-user-123',
    language: 'en',
    farms: [{
      id: 'test-farm-456',
      name: 'Test Farm'
    }]
  };

  try {
    console.log('📝 Sending request:', testData.prompt);
    
    const response = await axios.post('http://localhost:3001/open-ai-chat', testData);
    
    console.log('✅ Response received');
    
    if (response.data.needsEmployeeSelection) {
      console.log('✅ Employee selection triggered as expected');
      console.log('📋 Employees available:', response.data.employeeList?.length || 0);
      console.log('📦 Task data:', response.data.context?.taskData?.title);
    } else {
      console.log('❌ Expected employee selection but got:', response.data);
    }

  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testTaskCreation();
  await testEmployeeSelection();
  console.log('\n🏁 Tests completed!');
}

runTests().catch(console.error);
