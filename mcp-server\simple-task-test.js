const axios = require('axios');

// Simple test to verify task management functionality
async function testTaskCreation() {
  console.log('🧪 Testing Task Management Functionality...\n');

  const testData = {
    prompt: 'Create a task for <PERSON> to clean the milking parlor tomorrow.',
    userId: 'test-user-123',
    language: 'en',
    farms: [{
      id: 'test-farm-456',
      name: 'Test Farm'
    }]
  };

  try {
    console.log('📝 Sending request:', testData.prompt);
    
    const response = await axios.post('http://localhost:3001/open-ai-chat', testData);
    
    console.log('✅ Response received');
    console.log('📄 Status:', response.status);
    
    if (response.data.error) {
      console.log('❌ Error:', response.data.message);
    } else if (response.data.needsEmployeeSelection) {
      console.log('👥 Employee selection required');
      console.log('📋 Available employees:', response.data.employeeList?.length || 0);
      console.log('📦 Task data stored:', !!response.data.context?.taskData);
    } else if (response.data.saved) {
      console.log('💾 Task saved successfully!');
      console.log('🆔 Database ID:', response.data.databaseId);
    } else {
      console.log('📄 Response message:', response.data.message?.substring(0, 200) + '...');
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server not running on port 3001');
      console.log('💡 Please start the server with: node index.js');
    } else {
      console.log('❌ Request failed:', error.message);
      if (error.response) {
        console.log('📄 Response status:', error.response.status);
        console.log('📄 Response data:', error.response.data);
      }
    }
  }
}

// Test without assignee to trigger employee selection
async function testEmployeeSelection() {
  console.log('\n\n👥 Testing Employee Selection Flow...\n');

  const testData = {
    prompt: 'New task: Fix the fence. It\'s high priority.',
    userId: 'test-user-123',
    language: 'en',
    farms: [{
      id: 'test-farm-456',
      name: 'Test Farm'
    }]
  };

  try {
    console.log('📝 Sending request:', testData.prompt);
    
    const response = await axios.post('http://localhost:3001/open-ai-chat', testData);
    
    console.log('✅ Response received');
    
    if (response.data.needsEmployeeSelection) {
      console.log('✅ Employee selection triggered as expected');
      console.log('📋 Employees available:', response.data.employeeList?.length || 0);
      console.log('📦 Task data:', response.data.context?.taskData?.title);
      console.log('🎨 Selection type:', response.data.selectionType);

      // Display employee list structure
      if (response.data.employeeList && response.data.employeeList.length > 0) {
        console.log('\n👥 Employee List Structure:');
        response.data.employeeList.forEach((emp, index) => {
          console.log(`  ${index + 1}. ${emp.label} (${emp.role}) - ID: ${emp.id}`);
          console.log(`     Image: ${emp.imageUri ? 'Available' : 'None'}`);
          console.log(`     Description: ${emp.description}`);
        });
      }
    } else {
      console.log('❌ Expected employee selection but got:', response.data);
    }

  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

// Test employee selection follow-up
async function testEmployeeSelectionFollowUp() {
  console.log('\n\n🔄 Testing Employee Selection Follow-up...\n');

  // First, trigger employee selection
  const initialRequest = {
    prompt: 'Create a task to check the water pump.',
    userId: 'test-user-123',
    language: 'en',
    farms: [{
      id: 'test-farm-456',
      name: 'Test Farm'
    }]
  };

  try {
    console.log('📝 Step 1: Creating task without assignee...');
    const response1 = await axios.post('http://localhost:3001/open-ai-chat', initialRequest);

    if (response1.data.needsEmployeeSelection && response1.data.employeeList?.length > 0) {
      console.log('✅ Employee selection triggered');

      // Simulate selecting the first employee
      const firstEmployee = response1.data.employeeList[0];
      console.log(`📝 Step 2: Selecting employee: ${firstEmployee.label} (${firstEmployee.id})`);

      const selectionRequest = {
        prompt: firstEmployee.id, // Send employee ID
        userId: 'test-user-123',
        language: 'en',
        selectedEmployeeId: firstEmployee.id, // Also send via dedicated field
        context: response1.data.context
      };

      const response2 = await axios.post('http://localhost:3001/open-ai-chat', selectionRequest);

      if (response2.data.saved) {
        console.log('✅ Task created successfully after employee selection!');
        console.log('🆔 Task ID:', response2.data.databaseId);
        console.log('👤 Assigned to:', firstEmployee.label);
      } else if (response2.data.needsEmployeeSelection) {
        console.log('🔄 Still needs employee selection - showing interface again');
      } else {
        console.log('❌ Unexpected response:', response2.data.message?.substring(0, 100));
      }
    } else {
      console.log('❌ Initial request did not trigger employee selection');
    }

  } catch (error) {
    console.log('❌ Employee selection follow-up test failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testTaskCreation();
  await testEmployeeSelection();
  await testEmployeeSelectionFollowUp();
  console.log('\n🏁 All tests completed!');
}

runTests().catch(console.error);
