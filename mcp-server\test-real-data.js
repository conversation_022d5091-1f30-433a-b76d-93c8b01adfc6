const axios = require('axios');

// Test with real data from your database
async function testWithRealData() {
  console.log('🧪 Testing Task Management with Real Database Data...\n');

  // Test data based on your actual database
  const realTestCases = [
    {
      name: 'Haven View Farm - Dr. <PERSON><PERSON> (Owner)',
      data: {
        prompt: 'Create a task to fix the fence.',
        userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', // Dr. <PERSON><PERSON>
        language: 'en',
        farms: [{
          id: '7owH6eTfGSYqaRpgIsQE', // Haven View.
          name: 'Haven View.',
          ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'
        }]
      },
      expectedEmployees: ['<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Dr. <PERSON><PERSON>']
    },
    {
      name: '<PERSON>u <PERSON>mma Farm - Sami ul Haq1 (Owner)',
      data: {
        prompt: 'Create a task to check the water pump.',
        userId: 'lSrCUukuZxe2sNIsTiMfpmP2Jfx2', // Sami ul Haq1
        language: 'en',
        farms: [{
          id: '1xIOv0RbZVfwrxgFbZG0', // <PERSON>u <PERSON>
          name: '<PERSON><PERSON>',
          ownerId: 'lSrCUukuZxe2sNIsTiMfpmP2Jfx2'
        }]
      },
      expectedEmployees: ['Shery', 'Sami ul Haq1', 'Malik Ehtisham']
    },
    {
      name: 'Dairy House Farm - Dr. Javad Ahmed (Owner)',
      data: {
        prompt: 'Create a task to clean the milking parlor.',
        userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', // Dr. Javad Ahmed
        language: 'en',
        farms: [{
          id: 'wFSF3LbHQwPYy6A0pu94', // Dairy House
          name: 'Dairy House',
          ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'
        }]
      },
      expectedEmployees: ['Ali', 'Hassan Nawaz', 'Haider Bajwa', 'Dr. Javad Ahmed']
    }
  ];

  for (const testCase of realTestCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`📝 Prompt: "${testCase.data.prompt}"`);
    console.log(`👤 User: ${testCase.data.userId}`);
    console.log(`🏡 Farm: ${testCase.data.farms[0].name} (${testCase.data.farms[0].id})`);
    console.log(`📋 Expected employees: ${testCase.expectedEmployees.join(', ')}`);

    try {
      const response = await axios.post('http://localhost:3001/open-ai-chat', testCase.data);
      
      if (response.data.error) {
        console.log('❌ Error:', response.data.message);
        if (response.data.debugInfo) {
          console.log('🔧 Debug info:', response.data.debugInfo);
        }
      } else if (response.data.needsEmployeeSelection) {
        console.log('✅ Employee selection triggered successfully!');
        console.log(`👥 Found ${response.data.employeeList?.length || 0} employees`);
        
        if (response.data.employeeList && response.data.employeeList.length > 0) {
          console.log('📋 Available employees:');
          response.data.employeeList.forEach((emp, index) => {
            console.log(`  ${index + 1}. ${emp.label} (${emp.role}) - ID: ${emp.id}`);
          });
          
          // Test selecting the first employee
          console.log(`\n🔄 Testing employee selection with: ${response.data.employeeList[0].label}`);
          
          const selectionResponse = await axios.post('http://localhost:3001/open-ai-chat', {
            prompt: response.data.employeeList[0].id,
            userId: testCase.data.userId,
            language: 'en',
            selectedEmployeeId: response.data.employeeList[0].id,
            context: response.data.context
          });
          
          if (selectionResponse.data.saved) {
            console.log('✅ Task created successfully!');
            console.log(`🆔 Task ID: ${selectionResponse.data.databaseId}`);
            console.log(`👤 Assigned to: ${response.data.employeeList[0].label}`);
          } else {
            console.log('❌ Task creation failed:', selectionResponse.data.message);
          }
          
        } else {
          console.log('❌ No employees in response');
        }
      } else {
        console.log('📄 Other response:', response.data.message?.substring(0, 100) + '...');
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server not running on port 3001');
        return;
      } else {
        console.log('❌ Request failed:', error.message);
        if (error.response) {
          console.log('📄 Response:', error.response.data);
        }
      }
    }
  }
}

// Test the specific case from your screenshot
async function testHavenViewCase() {
  console.log('\n\n🎯 Testing Specific Case from Screenshot...\n');
  
  const havenViewTest = {
    prompt: 'Create a task to fix the fence.',
    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', // Dr. Javad Ahmed (owner of Haven View)
    language: 'en',
    farms: [{
      id: '7owH6eTfGSYqaRpgIsQE', // Haven View. (exact ID from database)
      name: 'Haven View.',
      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'
    }]
  };

  console.log('📝 Request data:', JSON.stringify(havenViewTest, null, 2));

  try {
    const response = await axios.post('http://localhost:3001/open-ai-chat', havenViewTest);
    
    console.log('\n✅ Response received:');
    console.log('📄 Message:', response.data.message);
    console.log('👥 Needs employee selection:', response.data.needsEmployeeSelection);
    console.log('📋 Employee count:', response.data.employeeList?.length || 0);
    
    if (response.data.employeeList) {
      console.log('\n👥 Employee List:');
      response.data.employeeList.forEach((emp, index) => {
        console.log(`  ${index + 1}. ${emp.label || emp.name} (${emp.role})`);
        console.log(`     ID: ${emp.id}`);
        console.log(`     Image: ${emp.imageUri ? 'Available' : 'None'}`);
      });
    }

    if (response.data.error) {
      console.log('❌ Error details:', response.data);
    }

  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('📄 Error response:', error.response.data);
    }
  }
}

// Run tests
async function runTests() {
  await testWithRealData();
  await testHavenViewCase();
  console.log('\n🏁 All tests completed!');
}

runTests().catch(console.error);
