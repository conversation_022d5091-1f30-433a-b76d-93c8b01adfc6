import React, { useState, useRef, useEffect, useCallback  } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Send, Paperclip, Mic, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useLookupStore } from '@/store/lookup-store';
import { useFocusEffect } from '@react-navigation/native';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  image?: string;
  isLoading?: boolean;
  animalInfo?: {
    name: string;
    species: string;
    id: string;
  };
  animalImages?: Array<{
    imageUri: string;
    name: string;
    species: string;
    id: string;
  }>;
}

export default function ChatScreen() {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const { user } = useAuthStore();
  const { farms, fetchFarms, getSelectedFarm } = useFarmStore();
  const { getLookupsByCategoryParsedData } = useLookupStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [lastAnimalData, setLastAnimalData] = useState<any | null>(null);
  const [lastFarmData, setLastFarmData] = useState<any | null>(null);
  const [healthCheckContext, setHealthCheckContext] = useState<any | null>(null);
  const flatListRef = useRef<FlatList>(null);

  // Refresh farms when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (user?.id) {
        fetchFarms(user.id);
      }
    }, [user?.id, fetchFarms])
  );

  // Get lookup data at component level
  const farmTypes = getLookupsByCategoryParsedData('farmType', 'farms.farmType.');
  const farmStatuses = getLookupsByCategoryParsedData('farmStatus', 'farms.farmStatus.');
  const sizeUnits = getLookupsByCategoryParsedData('areaUnit', 'farms.areaUnit.');

  // Calculate default IDs at component level
  const defaultFarmType = farmTypes.find(item => item.id === 'livestock') || farmTypes[0];
  const defaultStatus = farmStatuses.find(item => item.label.toLowerCase() === 'active') || farmStatuses[0];
  const defaultSizeUnit = sizeUnits.find(item => item.label.toLowerCase() === 'acre') || sizeUnits[0];

  const sendMessage = async () => {
    if (!inputText.trim() && !selectedImage) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim() || (selectedImage ? t('chat.imageAnalysis') : ''),
      isUser: true,
      timestamp: new Date(),
      image: selectedImage || undefined,
    };
    setMessages(prev => [...prev, userMessage]);
    const messageText = inputText.trim();
    const imageToAnalyze = selectedImage;
    setInputText('');
    setSelectedImage(null);
    setIsLoading(true);

    // Add loading message with animated dots
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      // Use the lookup data calculated at component level
      const selectedFarm = getSelectedFarm();
      const requestBody: any = {
        language: language,
        userId: user?.id,
        farms: selectedFarm ? [{
          id: selectedFarm.id,
          name: selectedFarm.name,
          location: selectedFarm.location
        }] : farms?.map(farm => ({
          id: farm.id,
          name: farm.name,
          location: farm.location
        })),
        previousAnimalData: lastAnimalData,
        previousFarmData: lastFarmData, // Add farm data
        defaultFarmTypeId: defaultFarmType?.id || "B2bye8PZBQYqscxXoVqZ",
        defaultStatusId: defaultStatus?.id || "tEbttSFNlpr6gGm5y66l",
        defaultSizeUnitId: defaultSizeUnit?.id || "QmzgdLcdPP0iEFVT5LyP",
        farmLocation: "", // Can be extracted from chat context if needed
        context: healthCheckContext // Pass health check context for animal selection and saving
      };

      if (imageToAnalyze) {
        requestBody.imageUri = imageToAnalyze;
        requestBody.prompt = messageText || (language === 'ur' 
          ? 'اس تصویر کا تجزیہ کریں'
          : 'Analyze this image');
      } else {
        requestBody.prompt = messageText;
      }

      const response = await fetch('http://localhost:3001/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      
      if (data.message) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image || undefined, // Include image if present (for health checks)
          animalInfo: data.animalInfo || undefined, // Include animal info if present
          animalImages: data.animalImages || undefined, // Include all animal images if present
        };
        setMessages(prev => [...prev, aiMessage]);

        // Handle different data types
        if (data.animalData) {
          setLastAnimalData(data.animalData);
          console.log('🐄 EXTRACTED ANIMAL DATA FOR DATABASE:');
          console.log(JSON.stringify(data.animalData, null, 2));
        }

        if (data.farmData) {
          setLastFarmData(data.farmData); // Store farm data
          console.log('🏡 EXTRACTED FARM DATA FOR DATABASE:');
          console.log(JSON.stringify(data.farmData, null, 2));
        }

        if (data.expenseData) {
          console.log('🧾 EXTRACTED EXPENSE DATA FOR DATABASE:');
          console.log(JSON.stringify(data.expenseData, null, 2));
        }

        // Handle health check context for animal selection and saving
        if (data.context) {
          setHealthCheckContext(data.context);
          console.log('🏥 HEALTH CHECK CONTEXT:', data.context);
        }

        // Log animal info for health checks
        if (data.animalInfo) {
          console.log('🐄 HEALTH CHECK ANIMAL INFO:', data.animalInfo);
        }

        // Clear health check context if health check was saved
        if (data.healthCheckSaved) {
          setHealthCheckContext(null);
          console.log('✅ Health check saved, context cleared');
        }
      } else {
        throw new Error('No message in response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t('chat.errorMessage'),
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePicker = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Please grant permission to access your photo library.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleVoicePress = () => {
    console.log('coming soon');
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
  };

  const handleAnimalSelection = async (animalId: string, animalName: string) => {
    // Send the animal ID as a message to select the animal
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${animalName}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      const selectedFarm = getSelectedFarm();
      const requestBody: any = {
        language: language,
        userId: user?.id,
        farms: selectedFarm ? [{
          id: selectedFarm.id,
          name: selectedFarm.name,
          location: selectedFarm.location
        }] : farms?.map(farm => ({
          id: farm.id,
          name: farm.name,
          location: farm.location
        })),
        previousAnimalData: lastAnimalData,
        previousFarmData: lastFarmData,
        defaultFarmTypeId: defaultFarmType?.id || "B2bye8PZBQYqscxXoVqZ",
        defaultStatusId: defaultStatus?.id || "tEbttSFNlpr6gGm5y66l",
        defaultSizeUnitId: defaultSizeUnit?.id || "QmzgdLcdPP0iEFVT5LyP",
        farmLocation: "",
        context: healthCheckContext,
        prompt: animalId // Send the animal ID instead of text
      };

      const response = await fetch('http://localhost:3001/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      if (data.message) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image || undefined,
          animalInfo: data.animalInfo || undefined,
          animalImages: data.animalImages || undefined,
        };
        setMessages(prev => [...prev, aiMessage]);

        // Handle context updates
        if (data.context) {
          setHealthCheckContext(data.context);
        }

        if (data.healthCheckSaved) {
          setHealthCheckContext(null);
        }
      }
    } catch (error) {
      console.error('Error selecting animal:', error);
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: language === 'ur' ? 'خرابی ہوئی۔ دوبارہ کوشش کریں۔' : 'Error occurred. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Add this component for loading animation
  const LoadingDots = () => {
    const [dots, setDots] = useState('');
    
    useEffect(() => {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '...') return '';
          return prev + '.';
        });
      }, 500);
      
      return () => clearInterval(interval);
    }, []);
    
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingBubble}>
          <Text style={styles.loadingText}>{dots || '.'}</Text>
        </View>
      </View>
    );
  };

  const renderMessage = ({ item }: { item: Message }) => {

    console.log("Data", item);
    return(
    <View style={[
      styles.messageContainer,
      item.isUser ? styles.userMessage : styles.aiMessage
    ]}>
      {item.image &&  (
        <View style={styles.imageContainer}>
          <Image source={{ uri: item.image }} style={styles.messageImage} />
          {item.animalInfo && (
            <View style={styles.animalInfoOverlay}>
              <Text style={styles.animalNameText}>
                🐄 {item.animalInfo.name} ({item.animalInfo.species})
              </Text>
            </View>
          )}
        </View>
      )}
      {item.animalImages && item.animalImages.length > 0 && (
        <View style={styles.animalImagesContainer}>
          {item.animalImages.map((animal, index) => (
            <TouchableOpacity
              key={`${animal.name}-${index}`}
              style={styles.animalImageRow}
              onPress={() => handleAnimalSelection(animal.id, animal.name)}
              activeOpacity={0.7}
            >
              <Image
                source={{ uri: animal.imageUri }}
                style={styles.animalImageInline}
                resizeMode="cover"
              />
              <Text style={styles.animalImageText}>
                {index + 1}. {animal.name} ({animal.species})
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
      {item.isLoading ? (
        <LoadingDots />
      ) : item.text ? (
        <Text style={[
          styles.messageText,
          item.isUser ? styles.userMessageText : styles.aiMessageText,
          language === 'ur' ? styles.urduText : null
        ]}>
          {item.text}
        </Text>
      ) : null}
    </View>
  )};

  const styles = getStyles(themedColors, language);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, language === 'ur' ? styles.urduText : null]}>
          {(() => {
            const selectedFarm = getSelectedFarm();
            if (selectedFarm) {
              return language === 'ur'
                ? `${selectedFarm.name} میں کام کر رہے ہیں`
                : `Working in ${selectedFarm.name}`;
            } else if (farms && farms.length > 0) {
              return language === 'ur'
                ? `${farms[0].name} میں کام کر رہے ہیں`
                : `Working in ${farms[0].name}`;
            } else {
              return t('chat.title');
            }
          })()}
        </Text>
        {farms && farms.length > 1 && (
          <Text style={[styles.headerSubtitle, language === 'ur' ? styles.urduText : null]}>
            {language === 'ur' 
              ? `${farms.length} فارمز دستیاب` 
              : `${farms.length} farms available`
            }
          </Text>
        )}
      </View>
      
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={item => item.id}
        style={styles.messagesList}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
      />
      
      {selectedImage && (
        <View style={styles.selectedImageContainer}>
          <Image source={{ uri: selectedImage }} style={styles.selectedImage} />
          <TouchableOpacity style={styles.removeImageButton} onPress={removeSelectedImage}>
            <X size={16} color="white" />
          </TouchableOpacity>
        </View>
      )}
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <TouchableOpacity style={styles.attachButton} onPress={handleImagePicker}>
            <Paperclip size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>
          
          <TextInput
            style={[
              styles.textInput,
              language === 'ur' ? styles.urduText : null
            ]}
            value={inputText}
            onChangeText={setInputText}
            placeholder={t('chat.inputPlaceholder')}
            placeholderTextColor={themedColors.textSecondary}
            multiline
            maxLength={1000}
          />
          
          <TouchableOpacity style={styles.voiceButton} onPress={handleVoicePress}>
            <Mic size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.sendButton, (!inputText.trim() && !selectedImage || isLoading) && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={(!inputText.trim() && !selectedImage) || isLoading}
          >
            <Send size={20} color="white" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: any, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    padding: 16,
    backgroundColor: themedColors.surface,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    color: themedColors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: themedColors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  urduText: {
    fontFamily: language === 'ur' ? 'NotoSansUrdu' : undefined,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  messagesList: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginVertical: 4,
    padding: 12,
    borderRadius: 16,
    maxWidth: '80%',
  },
  userMessage: {
    backgroundColor: themedColors.primary,
    alignSelf: 'flex-end',
  },
  aiMessage: {
    backgroundColor: themedColors.surface,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  messageText: {
    fontSize: 16,
  },
  userMessageText: {
    color: 'white',
  },
  aiMessageText: {
    color: themedColors.text,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedImageContainer: {
    margin: 16,
    position: 'relative',
  },
  selectedImage: {
    width: 100,
    height: 75,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: themedColors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    backgroundColor: themedColors.surface,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
  inputRow: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'flex-end',
    gap: 8,
  },
  attachButton: {
    padding: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    color: themedColors.text,
    backgroundColor: themedColors.background,
  },
  voiceButton: {
    padding: 8,
  },
  sendButton: {
    backgroundColor: themedColors.primary,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: themedColors.textSecondary,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 10,
  },
  // loadingBubble: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   backgroundColor: themedColors.primary,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  loadingBubble: {
    backgroundColor: themedColors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loadingText: {
    color: 'white',
    fontSize: 40,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  animalInfoOverlay: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  animalNameText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  animalImagesContainer: {
    marginVertical: 8,
    paddingHorizontal: 8,
  },
  animalImageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 8,
    backgroundColor: themedColors.surface,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  animalImageInline: {
    width: 40,
    height: 40,
    borderRadius: 6,
    marginRight: 8,
  },
  animalImageText: {
    fontSize: 14,
    color: themedColors.text,
    flex: 1,
  },
});












