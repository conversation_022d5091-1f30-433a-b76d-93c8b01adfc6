# Frontend Integration Example for Task Management

## Visual Employee Selection Interface

When the mcp-server returns `needsEmployeeSelection: true`, the frontend should display a visual selection interface instead of asking for text input.

### Expected Server Response
```json
{
  "message": "Please select an employee from the list below:",
  "needsEmployeeSelection": true,
  "selectionType": "employee_dropdown",
  "employeeList": [
    {
      "id": "emp-123",
      "label": "John Doe",
      "imageUri": "https://example.com/john.jpg",
      "role": "caretaker",
      "description": "Caretaker • Green Valley Farm"
    },
    {
      "id": "emp-456", 
      "label": "<PERSON>",
      "imageUri": null,
      "role": "admin",
      "description": "Admin • Green Valley Farm"
    }
  ],
  "context": {
    "taskData": {
      "title": "Fix the fence",
      "farmId": "farm-123",
      "farmName": "Green Valley Farm",
      "priority": "high"
    },
    "needsEmployeeSelection": true
  }
}
```

### Frontend Implementation

#### 1. Detection
```typescript
// In your chat response handler
if (response.needsEmployeeSelection && response.selectionType === 'employee_dropdown') {
  // Show visual employee selection interface
  showEmployeeSelectionModal(response.employeeList, response.context);
} else {
  // Handle normal chat response
  displayChatMessage(response.message);
}
```

#### 2. Visual Interface (React Native Example)
```tsx
const EmployeeSelectionModal = ({ employees, context, onSelect }) => {
  return (
    <Modal visible={true} animationType="slide">
      <View style={styles.container}>
        <Text style={styles.title}>Select Employee for Task</Text>
        <Text style={styles.taskTitle}>{context.taskData.title}</Text>
        
        <GenericDropdown
          placeholder="Select an employee"
          items={employees.map(emp => ({
            id: emp.id,
            label: emp.label,
            imageUri: emp.imageUri,
            customItem: (
              <View style={styles.employeeItem}>
                {emp.imageUri ? (
                  <Image source={{ uri: emp.imageUri }} style={styles.photo} />
                ) : (
                  <View style={styles.photoPlaceholder}>
                    <Text>{emp.label.charAt(0)}</Text>
                  </View>
                )}
                <View>
                  <Text style={styles.name}>{emp.label}</Text>
                  <Text style={styles.role}>{emp.description}</Text>
                </View>
              </View>
            )
          }))}
          onSelect={(employeeId) => onSelect(employeeId)}
          modalTitle="Select Employee"
        />
      </View>
    </Modal>
  );
};
```

#### 3. Selection Handler
```typescript
const handleEmployeeSelection = async (employeeId: string) => {
  try {
    const response = await fetch('/open-ai-chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: '', // Can be empty when using selectedEmployeeId
        selectedEmployeeId: employeeId, // Preferred method
        userId: currentUser.id,
        language: 'en',
        context: storedContext // The context from the previous response
      })
    });

    const result = await response.json();
    
    if (result.saved) {
      // Task created successfully
      showSuccessMessage(`Task "${result.taskData.title}" assigned to employee`);
      closeEmployeeSelectionModal();
    } else if (result.needsEmployeeSelection) {
      // Still needs selection (error case)
      showEmployeeSelectionModal(result.employeeList, result.context);
    }
  } catch (error) {
    showErrorMessage('Failed to assign task');
  }
};
```

### Visual Flow Example

```
User: "Create a task to fix the fence"
                ↓
Server: Detects no assignee mentioned
                ↓
Server Response: {
  needsEmployeeSelection: true,
  selectionType: "employee_dropdown",
  employeeList: [...]
}
                ↓
Frontend: Shows visual dropdown with employee photos/names
                ↓
User: Clicks on "John Doe" from the list
                ↓
Frontend: Sends selectedEmployeeId: "emp-123"
                ↓
Server: Creates task assigned to John Doe
                ↓
Server Response: {
  saved: true,
  message: "✅ Task Created Successfully...",
  taskData: {...}
}
                ↓
Frontend: Shows success message and closes modal
```

### Key Benefits

1. **Visual Selection**: Users see employee photos and names instead of typing
2. **Error Prevention**: No typos in employee names
3. **Rich Information**: Shows role and farm context for each employee
4. **Consistent UX**: Matches existing dropdown patterns in the app
5. **Accessibility**: Works with screen readers and keyboard navigation

### Integration with Existing Components

The response format is designed to work seamlessly with existing components:

- **GenericDropdown**: Direct compatibility with `items` array format
- **Employee Management**: Same data structure as employee lists
- **Task Creation**: Follows same patterns as manual task creation form
- **Multi-language**: Supports both English and Urdu interfaces

### Error Handling

If employee selection fails, the server will re-display the selection interface:

```json
{
  "message": "❌ Selected employee not found. Please select from the list:",
  "needsEmployeeSelection": true,
  "employeeList": [...],
  "error": false
}
```

Frontend should handle this by keeping the selection modal open and showing the error message.
