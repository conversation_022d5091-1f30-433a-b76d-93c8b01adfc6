require('dotenv').config();
const admin = require('firebase-admin');

// Initialize Firebase Admin (same as in index.js)
const serviceAccount = require('./firebase-service-account.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  storageBucket: process.env.STORAGE_BUCKET || 'kissandost-9570f.firebasestorage.app'
});

const firestore = admin.firestore();

async function checkDatabaseSetup() {
  console.log('🔍 Checking Database Setup for Task Management...\n');

  try {
    // Check farms
    console.log('📋 Checking Farms...');
    const farmsSnapshot = await firestore.collection('farms').get();
    console.log(`Found ${farmsSnapshot.docs.length} farms:`);
    
    const farms = [];
    farmsSnapshot.docs.forEach(doc => {
      const farmData = doc.data();
      farms.push({
        id: doc.id,
        name: farmData.name,
        ownerId: farmData.ownerId
      });
      console.log(`  - ${farmData.name} (ID: ${doc.id}, Owner: ${farmData.ownerId})`);
    });

    // Check users
    console.log('\n👥 Checking Users...');
    const usersSnapshot = await firestore.collection('users').get();
    console.log(`Found ${usersSnapshot.docs.length} users:`);
    
    const users = [];
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      users.push({
        id: doc.id,
        name: userData.name,
        role: userData.role,
        assignedFarmIds: userData.assignedFarmIds || []
      });
      console.log(`  - ${userData.name} (ID: ${doc.id}, Role: ${userData.role})`);
      console.log(`    Assigned Farms: ${(userData.assignedFarmIds || []).join(', ') || 'None'}`);
    });

    // Check relationships
    console.log('\n🔗 Checking Farm-User Relationships...');
    farms.forEach(farm => {
      console.log(`\nFarm: ${farm.name} (${farm.id})`);
      console.log(`Owner: ${farm.ownerId}`);
      
      // Find owner
      const owner = users.find(u => u.id === farm.ownerId);
      if (owner) {
        console.log(`  ✅ Owner found: ${owner.name} (${owner.role})`);
      } else {
        console.log(`  ❌ Owner not found in users collection`);
      }
      
      // Find assigned employees
      const assignedEmployees = users.filter(u => 
        u.assignedFarmIds && u.assignedFarmIds.includes(farm.id)
      );
      
      console.log(`  👥 Assigned employees: ${assignedEmployees.length}`);
      assignedEmployees.forEach(emp => {
        console.log(`    - ${emp.name} (${emp.role})`);
      });
      
      if (assignedEmployees.length === 0) {
        console.log(`    ⚠️  No employees assigned to this farm`);
      }
    });

    // Provide recommendations
    console.log('\n💡 Recommendations for Task Management:');
    
    if (farms.length === 0) {
      console.log('❌ No farms found. Create a farm first.');
    } else {
      const farmsWithoutEmployees = farms.filter(farm => {
        const assignedEmployees = users.filter(u => 
          u.assignedFarmIds && u.assignedFarmIds.includes(farm.id)
        );
        return assignedEmployees.length === 0;
      });
      
      if (farmsWithoutEmployees.length > 0) {
        console.log('⚠️  Farms without assigned employees:');
        farmsWithoutEmployees.forEach(farm => {
          console.log(`   - ${farm.name} (${farm.id})`);
        });
        console.log('   Solution: Assign employees to these farms or ensure farm owners are in assignedFarmIds');
      }
      
      // Check if farm owners have themselves in assignedFarmIds
      farms.forEach(farm => {
        const owner = users.find(u => u.id === farm.ownerId);
        if (owner && (!owner.assignedFarmIds || !owner.assignedFarmIds.includes(farm.id))) {
          console.log(`⚠️  Farm owner ${owner.name} is not assigned to their own farm ${farm.name}`);
          console.log(`   Solution: Add farm ID ${farm.id} to user ${owner.id}'s assignedFarmIds array`);
        }
      });
    }

    // Generate test data for the current setup
    if (farms.length > 0 && users.length > 0) {
      const testFarm = farms[0];
      const testUser = users.find(u => u.id === testFarm.ownerId) || users[0];
      
      console.log('\n🧪 Test Data for Task Creation:');
      console.log('Use this data in your frontend or test scripts:');
      console.log(JSON.stringify({
        prompt: 'Create a task to fix the fence.',
        userId: testUser.id,
        language: 'en',
        farms: [{
          id: testFarm.id,
          name: testFarm.name,
          ownerId: testFarm.ownerId
        }]
      }, null, 2));
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  }

  console.log('\n🏁 Database check completed!');
  process.exit(0);
}

checkDatabaseSetup();
