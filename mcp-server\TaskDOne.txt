1: Add Farm  DOne
2: Add Animal DOne
3: Health Check  DOne
4: Milking DOne   FOund issue
5: Expense DOne
6:Pregnancy   (Add Pregnancy for ANimal-name, <PERSON><PERSON>uc is confirmed, concemtion date is yesterday)
7: Task
User: "Create a task to fix the fence"
Server: "Who should this task be assigned to?"
User: Types "john" 
Server: "Employee 'john' not found"

User: "Create a task to fix the fence"
Server: "Who should this task be assigned to?"
User: Types "john" 
Server: "Employee 'john' not found"